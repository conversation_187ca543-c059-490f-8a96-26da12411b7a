#![allow(dead_code)]
#![allow(unused_variables)]
#![allow(unused_parens)]
#![allow(unused_must_use)]
#![allow(unused_parens)]

use regex::Regex;
use colored::*;
use std::{
    ffi::OsString,
    fmt::Debug,
    fs::{self},
    path::{self, Path, PathBuf},
};

// main function is a placeholder.
fn main() {}

// Recursively searches for video files (mp4, mkv, avi) in a directory tree.
// For each video file without a corresponding .srt subtitle, calls check_and_create_subtitle_for_file.
pub fn find_files<'a>(path: impl AsRef<Path> + Debug) {
    if let Ok(entries) = fs::read_dir(&path) {
        for entry in entries {
            if let Ok(entry) = entry {
                if (entry.file_type().unwrap().is_dir()) {
                    if entry
                        .file_name()
                        .to_string_lossy()
                        .to_lowercase()
                        .ends_with("_pics")
                        == false
                    {
                        find_files(&entry.path().to_str().unwrap());
                    }
                } else if (entry.file_type().unwrap().is_file()) {
                    let ext = entry
                        .path()
                        .extension()
                        .unwrap_or_default()
                        .to_string_lossy()
                        .to_lowercase();
                    if (ext == "mp4" || ext == "mkv" || ext == "avi")
                        && entry.path().with_extension("srt").exists() == false
                    {
                        println!("🎬 Found video: {}", entry.file_name().to_string_lossy().white().bold());
                        check_and_create_subtitle_for_file(entry.path());
                    }
                } else {
                    panic!("Unknown entry {:?} : ", entry.path().file_name());
                }
            }
        }
    }

    // if let Ok(entries) = fs::read_dir(&path) {
    //     for entry in entries {
    //         if let Ok(entry) = entry {}
    //     }
    // }
}

// For a given video file, looks for matching subtitle files in a 'subs' subdirectory.
// If found, copies the best-matching subtitle(s) to the video's directory, renaming them to match the video.
fn check_and_create_subtitle_for_file<'a>(path: std::path::PathBuf) {
    let filename_wo_ext = &path.file_stem().unwrap().to_str().unwrap();
    let folder = &path.parent().unwrap().to_str().unwrap();
    let subtitle_folder: PathBuf;
    if (path
        .as_path()
        .parent()
        .unwrap()
        .join("subs")
        .join(filename_wo_ext)
        .exists())
    {
        subtitle_folder = path
            .as_path()
            .parent()
            .unwrap()
            .join("subs")
            .join(filename_wo_ext);
    } else if (path.as_path().parent().unwrap().join("subs").exists()) {
        subtitle_folder = path.as_path().parent().unwrap().join("subs");
    } else {
        return;
    }
    let mut vector: Vec<OsString> = Vec::new();
    let all_files_in_subfolder = find_all_files_in_subfolder(&subtitle_folder, &mut vector);
    let possibles: Vec<OsString> =
        possible_subtitles_for_file(filename_wo_ext, all_files_in_subfolder);
    if possibles.len() > 0 {
        for i in 0..possibles.len() {
            let subtitle = possibles.get(i).unwrap();
            let subtitle_newname;
            let pathh = &path.parent().unwrap();
            if (i == 0) {
                subtitle_newname = pathh.join(format!("{filename_wo_ext}.srt"));
            } else {
                subtitle_newname = pathh.join(format!("{filename_wo_ext}_{}.srt", i + 1));
            }
            fs::copy(subtitle, subtitle_newname);
        }
        // Colorful output using the colored crate
        println!(
            "  ✅ {} {} {} {}",
            filename_wo_ext.cyan().bold(),
            "copied".yellow().bold(),
            possibles.len().to_string().green().bold(),
            if possibles.len() == 1 { "subtitle" } else { "subtitles" }.magenta().bold()
        );
    }
}

// Filters a list of subtitle files, returning those that match the video filename and contain language tags (en|english).
fn possible_subtitles_for_file(filename_wo_ext: &str, vector: &Vec<OsString>) -> Vec<OsString> {
    let lang_set = "(en|english)".to_string();
    let filestemsafe = filename_wo_ext.replace("-", ".+");
    let format = format!(r"(?i).*{filestemsafe}.*(?:{lang_set})\b.*srt$");
    let re = Regex::new(format.as_str()).unwrap();
    let valids = vector
        .iter()
        .filter(|f| re.is_match(&f.to_string_lossy()))
        .map(|s| s.to_os_string())
        .take(1)
        .collect();
    valids
}

// Recursively collects all files in a given folder (and subfolders) that are larger than 4KB.
fn find_all_files_in_subfolder<'a>(
    subtitle_folder: &'a path::PathBuf,
    vector: &'a mut Vec<OsString>,
) -> &'a Vec<OsString> {
    if let Ok(entries) = fs::read_dir(subtitle_folder) {
        for entry in entries {
            if let Ok(entry) = entry {
                if entry.file_type().unwrap().is_dir() {
                    find_all_files_in_subfolder(&entry.path(), vector);
                } else if entry.file_type().unwrap().is_file() {
                    let meta = entry.metadata().unwrap();
                    if (meta.len() > 4096) {
                        vector.push(entry.path().into_os_string());
                    }
                }
            }
        }
    }
    vector
}
