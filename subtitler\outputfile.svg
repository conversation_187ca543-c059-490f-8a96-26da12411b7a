<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg version="1.1" width="1200" height="790" onload="init(evt)" viewBox="0 0 1200 790" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:fg="http://github.com/jonhoo/inferno"><!--Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples.--><!--NOTES: --><defs><linearGradient id="background" y1="0" y2="1" x1="0" x2="0"><stop stop-color="#eeeeee" offset="5%"/><stop stop-color="#eeeeb0" offset="95%"/></linearGradient></defs><style type="text/css">
text { font-family:"Verdana"; font-size:12px; fill:rgb(0,0,0); }
#title { text-anchor:middle; font-size:17px; }
#search { opacity:0.1; cursor:pointer; }
#search:hover, #search.show { opacity:1; }
#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
#unzoom { cursor:pointer; }
#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
.hide { display:none; }
.parent { opacity:0.5; }
</style><script type="text/ecmascript"><![CDATA[
        var nametype = 'Function:';
        var fontsize = 12;
        var fontwidth = 0.59;
        var xpad = 10;
        var inverted = false;
        var searchcolor = 'rgb(230,0,230)';
        var fluiddrawing = true;
        var truncate_text_right = false;
    ]]><![CDATA["use strict";
var details, searchbtn, unzoombtn, matchedtxt, svg, searching, frames;
function init(evt) {
    details = document.getElementById("details").firstChild;
    searchbtn = document.getElementById("search");
    unzoombtn = document.getElementById("unzoom");
    matchedtxt = document.getElementById("matched");
    svg = document.getElementsByTagName("svg")[0];
    frames = document.getElementById("frames");
    total_samples = parseInt(frames.attributes.total_samples.value);
    searching = 0;

    // Use GET parameters to restore a flamegraph's state.
    var restore_state = function() {
        var params = get_params();
        if (params.x && params.y)
            zoom(find_group(document.querySelector('[*|x="' + params.x + '"][y="' + params.y + '"]')));
        if (params.s)
            search(params.s);
    };

    if (fluiddrawing) {
        // Make width dynamic so the SVG fits its parent's width.
        svg.removeAttribute("width");
        // Edge requires us to have a viewBox that gets updated with size changes.
        var isEdge = /Edge\/\d./i.test(navigator.userAgent);
        var update_for_width_change = function() {
            if (isEdge) {
                svg.attributes.viewBox.value = "0 0 " + svg.width.baseVal.value + " " + svg.height.baseVal.value;
            }

            // Keep consistent padding on left and right of frames container.
            frames.attributes.width.value = svg.width.baseVal.value - xpad * 2;

            // Text truncation needs to be adjusted for the current width.
            var el = frames.children;
            for(var i = 0; i < el.length; i++) {
                update_text(el[i]);
            }

            // Keep search elements at a fixed distance from right edge.
            var svgWidth = svg.width.baseVal.value;
            searchbtn.attributes.x.value = svgWidth - xpad - 100;
            matchedtxt.attributes.x.value = svgWidth - xpad - 100;
        };
        window.addEventListener('resize', function() {
            update_for_width_change();
        });
        // This needs to be done asynchronously for Safari to work.
        setTimeout(function() {
            unzoom();
            update_for_width_change();
            restore_state();
            if (!isEdge) {
                svg.removeAttribute("viewBox");
            }
        }, 0);
    } else {
        restore_state();
    }
}
// event listeners
window.addEventListener("click", function(e) {
    var target = find_group(e.target);
    if (target) {
        if (target.nodeName == "a") {
            if (e.ctrlKey === false) return;
            e.preventDefault();
        }
        if (target.classList.contains("parent")) unzoom();
        zoom(target);

        // set parameters for zoom state
        var el = target.querySelector("rect");
        if (el && el.attributes && el.attributes.y && el.attributes["fg:x"]) {
            var params = get_params()
            params.x = el.attributes["fg:x"].value;
            params.y = el.attributes.y.value;
            history.replaceState(null, null, parse_params(params));
        }
    }
    else if (e.target.id == "unzoom") {
        unzoom();

        // remove zoom state
        var params = get_params();
        if (params.x) delete params.x;
        if (params.y) delete params.y;
        history.replaceState(null, null, parse_params(params));
    }
    else if (e.target.id == "search") search_prompt();
}, false)
// mouse-over for info
// show
window.addEventListener("mouseover", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = nametype + " " + g_to_text(target);
}, false)
// clear
window.addEventListener("mouseout", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = ' ';
}, false)
// ctrl-F for search
window.addEventListener("keydown",function (e) {
    if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
        e.preventDefault();
        search_prompt();
    }
}, false)
// functions
function get_params() {
    var params = {};
    var paramsarr = window.location.search.substr(1).split('&');
    for (var i = 0; i < paramsarr.length; ++i) {
        var tmp = paramsarr[i].split("=");
        if (!tmp[0] || !tmp[1]) continue;
        params[tmp[0]]  = decodeURIComponent(tmp[1]);
    }
    return params;
}
function parse_params(params) {
    var uri = "?";
    for (var key in params) {
        uri += key + '=' + encodeURIComponent(params[key]) + '&';
    }
    if (uri.slice(-1) == "&")
        uri = uri.substring(0, uri.length - 1);
    if (uri == '?')
        uri = window.location.href.split('?')[0];
    return uri;
}
function find_child(node, selector) {
    var children = node.querySelectorAll(selector);
    if (children.length) return children[0];
    return;
}
function find_group(node) {
    var parent = node.parentElement;
    if (!parent) return;
    if (parent.id == "frames") return node;
    return find_group(parent);
}
function orig_save(e, attr, val) {
    if (e.attributes["fg:orig_" + attr] != undefined) return;
    if (e.attributes[attr] == undefined) return;
    if (val == undefined) val = e.attributes[attr].value;
    e.setAttribute("fg:orig_" + attr, val);
}
function orig_load(e, attr) {
    if (e.attributes["fg:orig_"+attr] == undefined) return;
    e.attributes[attr].value = e.attributes["fg:orig_" + attr].value;
    e.removeAttribute("fg:orig_" + attr);
}
function g_to_text(e) {
    var text = find_child(e, "title").firstChild.nodeValue;
    return (text)
}
function g_to_func(e) {
    var func = g_to_text(e);
    // if there's any manipulation we want to do to the function
    // name before it's searched, do it here before returning.
    return (func);
}
function update_text(e) {
    var r = find_child(e, "rect");
    var t = find_child(e, "text");
    var w = parseFloat(r.attributes.width.value) * frames.attributes.width.value / 100 - 3;
    var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
    t.attributes.x.value = format_percent((parseFloat(r.attributes.x.value) + (100 * 3 / frames.attributes.width.value)));
    // Smaller than this size won't fit anything
    if (w < 2 * fontsize * fontwidth) {
        t.textContent = "";
        return;
    }
    t.textContent = txt;
    // Fit in full text width
    if (/^ *\$/.test(txt) || t.getComputedTextLength() < w)
        return;
    if (truncate_text_right) {
        // Truncate the right side of the text.
        for (var x = txt.length - 2; x > 0; x--) {
            if (t.getSubStringLength(0, x + 2) <= w) {
                t.textContent = txt.substring(0, x) + "..";
                return;
            }
        }
    } else {
        // Truncate the left side of the text.
        for (var x = 2; x < txt.length; x++) {
            if (t.getSubStringLength(x - 2, txt.length) <= w) {
                t.textContent = ".." + txt.substring(x, txt.length);
                return;
            }
        }
    }
    t.textContent = "";
}
// zoom
function zoom_reset(e) {
    if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * parseInt(e.attributes["fg:x"].value) / total_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / total_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_reset(c[i]);
    }
}
function zoom_child(e, x, zoomed_width_samples) {
    if (e.tagName == "text") {
        var parent_x = parseFloat(find_child(e.parentNode, "rect[x]").attributes.x.value);
        e.attributes.x.value = format_percent(parent_x + (100 * 3 / frames.attributes.width.value));
    } else if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * (parseInt(e.attributes["fg:x"].value) - x) / zoomed_width_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / zoomed_width_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_child(c[i], x, zoomed_width_samples);
    }
}
function zoom_parent(e) {
    if (e.attributes) {
        if (e.attributes.x != undefined) {
            e.attributes.x.value = "0.0%";
        }
        if (e.attributes.width != undefined) {
            e.attributes.width.value = "100.0%";
        }
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_parent(c[i]);
    }
}
function zoom(node) {
    var attr = find_child(node, "rect").attributes;
    var width = parseInt(attr["fg:w"].value);
    var xmin = parseInt(attr["fg:x"].value);
    var xmax = xmin + width;
    var ymin = parseFloat(attr.y.value);
    unzoombtn.classList.remove("hide");
    var el = frames.children;
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        var a = find_child(e, "rect").attributes;
        var ex = parseInt(a["fg:x"].value);
        var ew = parseInt(a["fg:w"].value);
        // Is it an ancestor
        if (!inverted) {
            var upstack = parseFloat(a.y.value) > ymin;
        } else {
            var upstack = parseFloat(a.y.value) < ymin;
        }
        if (upstack) {
            // Direct ancestor
            if (ex <= xmin && (ex+ew) >= xmax) {
                e.classList.add("parent");
                zoom_parent(e);
                update_text(e);
            }
            // not in current path
            else
                e.classList.add("hide");
        }
        // Children maybe
        else {
            // no common path
            if (ex < xmin || ex >= xmax) {
                e.classList.add("hide");
            }
            else {
                zoom_child(e, xmin, width);
                update_text(e);
            }
        }
    }
}
function unzoom() {
    unzoombtn.classList.add("hide");
    var el = frames.children;
    for(var i = 0; i < el.length; i++) {
        el[i].classList.remove("parent");
        el[i].classList.remove("hide");
        zoom_reset(el[i]);
        update_text(el[i]);
    }
}
// search
function reset_search() {
    var el = document.querySelectorAll("#frames rect");
    for (var i = 0; i < el.length; i++) {
        orig_load(el[i], "fill")
    }
    var params = get_params();
    delete params.s;
    history.replaceState(null, null, parse_params(params));
}
function search_prompt() {
    if (!searching) {
        var term = prompt("Enter a search term (regexp " +
            "allowed, eg: ^ext4_)", "");
        if (term != null) {
            search(term)
        }
    } else {
        reset_search();
        searching = 0;
        searchbtn.classList.remove("show");
        searchbtn.firstChild.nodeValue = "Search"
        matchedtxt.classList.add("hide");
        matchedtxt.firstChild.nodeValue = ""
    }
}
function search(term) {
    var re = new RegExp(term);
    var el = frames.children;
    var matches = new Object();
    var maxwidth = 0;
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        // Skip over frames which are either not visible, or below the zoomed-to frame
        if (e.classList.contains("hide") || e.classList.contains("parent")) {
            continue;
        }
        var func = g_to_func(e);
        var rect = find_child(e, "rect");
        if (func == null || rect == null)
            continue;
        // Save max width. Only works as we have a root frame
        var w = parseInt(rect.attributes["fg:w"].value);
        if (w > maxwidth)
            maxwidth = w;
        if (func.match(re)) {
            // highlight
            var x = parseInt(rect.attributes["fg:x"].value);
            orig_save(rect, "fill");
            rect.attributes.fill.value = searchcolor;
            // remember matches
            if (matches[x] == undefined) {
                matches[x] = w;
            } else {
                if (w > matches[x]) {
                    // overwrite with parent
                    matches[x] = w;
                }
            }
            searching = 1;
        }
    }
    if (!searching)
        return;
    var params = get_params();
    params.s = term;
    history.replaceState(null, null, parse_params(params));

    searchbtn.classList.add("show");
    searchbtn.firstChild.nodeValue = "Reset Search";
    // calculate percent matched, excluding vertical overlap
    var count = 0;
    var lastx = -1;
    var lastw = 0;
    var keys = Array();
    for (k in matches) {
        if (matches.hasOwnProperty(k))
            keys.push(k);
    }
    // sort the matched frames by their x location
    // ascending, then width descending
    keys.sort(function(a, b){
        return a - b;
    });
    // Step through frames saving only the biggest bottom-up frames
    // thanks to the sort order. This relies on the tree property
    // where children are always smaller than their parents.
    for (var k in keys) {
        var x = parseInt(keys[k]);
        var w = matches[keys[k]];
        if (x >= lastx + lastw) {
            count += w;
            lastx = x;
            lastw = w;
        }
    }
    // display matched percent
    matchedtxt.classList.remove("hide");
    var pct = 100 * count / maxwidth;
    if (pct != 100) pct = pct.toFixed(1);
    matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
}
function format_percent(n) {
    return n.toFixed(4) + "%";
}
]]></script><rect x="0" y="0" width="100%" height="790" fill="url(#background)"/><text id="title" x="50.0000%" y="24.00">Flame Graph</text><text id="details" x="10" y="773.00"> </text><text id="unzoom" class="hide" x="10" y="24.00">Reset Zoom</text><text id="search" x="1090" y="24.00">Search</text><text id="matched" x="1090" y="773.00"> </text><svg id="frames" x="10" width="1180" total_samples="55"><g><title>std::fs::DirEntry::path (1 samples, 1.82%)</title><rect x="0.0000%" y="421" width="1.8182%" height="15" fill="rgb(227,0,7)" fg:x="0" fg:w="1"/><text x="0.2500%" y="431.50">s..</text></g><g><title>std::sys::unix::fs::DirEntry::path (1 samples, 1.82%)</title><rect x="0.0000%" y="405" width="1.8182%" height="15" fill="rgb(217,0,24)" fg:x="0" fg:w="1"/><text x="0.2500%" y="415.50">s..</text></g><g><title>std::path::Path::join (1 samples, 1.82%)</title><rect x="0.0000%" y="389" width="1.8182%" height="15" fill="rgb(221,193,54)" fg:x="0" fg:w="1"/><text x="0.2500%" y="399.50">s..</text></g><g><title>std::path::Path::_join (1 samples, 1.82%)</title><rect x="0.0000%" y="373" width="1.8182%" height="15" fill="rgb(248,212,6)" fg:x="0" fg:w="1"/><text x="0.2500%" y="383.50">s..</text></g><g><title>std::path::PathBuf::push (1 samples, 1.82%)</title><rect x="0.0000%" y="357" width="1.8182%" height="15" fill="rgb(208,68,35)" fg:x="0" fg:w="1"/><text x="0.2500%" y="367.50">s..</text></g><g><title>std::path::PathBuf::_push (1 samples, 1.82%)</title><rect x="0.0000%" y="341" width="1.8182%" height="15" fill="rgb(232,128,0)" fg:x="0" fg:w="1"/><text x="0.2500%" y="351.50">s..</text></g><g><title>std::ffi::os_str::OsString::push (1 samples, 1.82%)</title><rect x="0.0000%" y="325" width="1.8182%" height="15" fill="rgb(207,160,47)" fg:x="0" fg:w="1"/><text x="0.2500%" y="335.50">s..</text></g><g><title>std::sys::unix::os_str::Buf::push_slice (1 samples, 1.82%)</title><rect x="0.0000%" y="309" width="1.8182%" height="15" fill="rgb(228,23,34)" fg:x="0" fg:w="1"/><text x="0.2500%" y="319.50">s..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::extend_from_slice (1 samples, 1.82%)</title><rect x="0.0000%" y="293" width="1.8182%" height="15" fill="rgb(218,30,26)" fg:x="0" fg:w="1"/><text x="0.2500%" y="303.50">a..</text></g><g><title>&lt;alloc::vec::Vec&lt;T,A&gt; as alloc::vec::spec_extend::SpecExtend&lt;&amp;T,core::slice::iter::Iter&lt;T&gt;&gt;&gt;::spec_extend (1 samples, 1.82%)</title><rect x="0.0000%" y="277" width="1.8182%" height="15" fill="rgb(220,122,19)" fg:x="0" fg:w="1"/><text x="0.2500%" y="287.50">&lt;..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::append_elements (1 samples, 1.82%)</title><rect x="0.0000%" y="261" width="1.8182%" height="15" fill="rgb(250,228,42)" fg:x="0" fg:w="1"/><text x="0.2500%" y="271.50">a..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::reserve (1 samples, 1.82%)</title><rect x="0.0000%" y="245" width="1.8182%" height="15" fill="rgb(240,193,28)" fg:x="0" fg:w="1"/><text x="0.2500%" y="255.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::reserve (1 samples, 1.82%)</title><rect x="0.0000%" y="229" width="1.8182%" height="15" fill="rgb(216,20,37)" fg:x="0" fg:w="1"/><text x="0.2500%" y="239.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::reserve::do_reserve_and_handle (1 samples, 1.82%)</title><rect x="0.0000%" y="213" width="1.8182%" height="15" fill="rgb(206,188,39)" fg:x="0" fg:w="1"/><text x="0.2500%" y="223.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::grow_amortized (1 samples, 1.82%)</title><rect x="0.0000%" y="197" width="1.8182%" height="15" fill="rgb(217,207,13)" fg:x="0" fg:w="1"/><text x="0.2500%" y="207.50">a..</text></g><g><title>alloc::raw_vec::finish_grow (1 samples, 1.82%)</title><rect x="0.0000%" y="181" width="1.8182%" height="15" fill="rgb(231,73,38)" fg:x="0" fg:w="1"/><text x="0.2500%" y="191.50">a..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::grow (1 samples, 1.82%)</title><rect x="0.0000%" y="165" width="1.8182%" height="15" fill="rgb(225,20,46)" fg:x="0" fg:w="1"/><text x="0.2500%" y="175.50">&lt;..</text></g><g><title>alloc::alloc::Global::grow_impl (1 samples, 1.82%)</title><rect x="0.0000%" y="149" width="1.8182%" height="15" fill="rgb(210,31,41)" fg:x="0" fg:w="1"/><text x="0.2500%" y="159.50">a..</text></g><g><title>alloc::alloc::realloc (1 samples, 1.82%)</title><rect x="0.0000%" y="133" width="1.8182%" height="15" fill="rgb(221,200,47)" fg:x="0" fg:w="1"/><text x="0.2500%" y="143.50">a..</text></g><g><title>realloc (1 samples, 1.82%)</title><rect x="0.0000%" y="117" width="1.8182%" height="15" fill="rgb(226,26,5)" fg:x="0" fg:w="1"/><text x="0.2500%" y="127.50">r..</text></g><g><title>pthread_attr_setschedparam (1 samples, 1.82%)</title><rect x="0.0000%" y="101" width="1.8182%" height="15" fill="rgb(249,33,26)" fg:x="0" fg:w="1"/><text x="0.2500%" y="111.50">p..</text></g><g><title>__nss_database_lookup (1 samples, 1.82%)</title><rect x="0.0000%" y="85" width="1.8182%" height="15" fill="rgb(235,183,28)" fg:x="0" fg:w="1"/><text x="0.2500%" y="95.50">_..</text></g><g><title>&lt;alloc::ffi::c_str::CString as core::convert::From&lt;&amp;core::ffi::c_str::CStr&gt;&gt;::from (2 samples, 3.64%)</title><rect x="1.8182%" y="373" width="3.6364%" height="15" fill="rgb(221,5,38)" fg:x="1" fg:w="2"/><text x="2.0682%" y="383.50">&lt;all..</text></g><g><title>alloc::ffi::c_str::&lt;impl alloc::borrow::ToOwned for core::ffi::c_str::CStr&gt;::to_owned (1 samples, 1.82%)</title><rect x="3.6364%" y="357" width="1.8182%" height="15" fill="rgb(247,18,42)" fg:x="2" fg:w="1"/><text x="3.8864%" y="367.50">a..</text></g><g><title>&lt;T as core::convert::Into&lt;U&gt;&gt;::into (1 samples, 1.82%)</title><rect x="3.6364%" y="341" width="1.8182%" height="15" fill="rgb(241,131,45)" fg:x="2" fg:w="1"/><text x="3.8864%" y="351.50">&lt;..</text></g><g><title>&lt;alloc::boxed::Box&lt;[T]&gt; as core::convert::From&lt;&amp;[T]&gt;&gt;::from (1 samples, 1.82%)</title><rect x="3.6364%" y="325" width="1.8182%" height="15" fill="rgb(249,31,29)" fg:x="2" fg:w="1"/><text x="3.8864%" y="335.50">&lt;..</text></g><g><title>alloc::raw_vec::RawVec&lt;T&gt;::with_capacity (1 samples, 1.82%)</title><rect x="3.6364%" y="309" width="1.8182%" height="15" fill="rgb(225,111,53)" fg:x="2" fg:w="1"/><text x="3.8864%" y="319.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::with_capacity_in (1 samples, 1.82%)</title><rect x="3.6364%" y="293" width="1.8182%" height="15" fill="rgb(238,160,17)" fg:x="2" fg:w="1"/><text x="3.8864%" y="303.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::allocate_in (1 samples, 1.82%)</title><rect x="3.6364%" y="277" width="1.8182%" height="15" fill="rgb(214,148,48)" fg:x="2" fg:w="1"/><text x="3.8864%" y="287.50">a..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::allocate (1 samples, 1.82%)</title><rect x="3.6364%" y="261" width="1.8182%" height="15" fill="rgb(232,36,49)" fg:x="2" fg:w="1"/><text x="3.8864%" y="271.50">&lt;..</text></g><g><title>alloc::alloc::Global::alloc_impl (1 samples, 1.82%)</title><rect x="3.6364%" y="245" width="1.8182%" height="15" fill="rgb(209,103,24)" fg:x="2" fg:w="1"/><text x="3.8864%" y="255.50">a..</text></g><g><title>alloc::alloc::alloc (1 samples, 1.82%)</title><rect x="3.6364%" y="229" width="1.8182%" height="15" fill="rgb(229,88,8)" fg:x="2" fg:w="1"/><text x="3.8864%" y="239.50">a..</text></g><g><title>__rust_alloc (1 samples, 1.82%)</title><rect x="3.6364%" y="213" width="1.8182%" height="15" fill="rgb(213,181,19)" fg:x="2" fg:w="1"/><text x="3.8864%" y="223.50">_..</text></g><g><title>&lt;std::fs::ReadDir as core::iter::traits::iterator::Iterator&gt;::next (3 samples, 5.45%)</title><rect x="1.8182%" y="405" width="5.4545%" height="15" fill="rgb(254,191,54)" fg:x="1" fg:w="3"/><text x="2.0682%" y="415.50">&lt;std::f..</text></g><g><title>&lt;std::sys::unix::fs::ReadDir as core::iter::traits::iterator::Iterator&gt;::next (3 samples, 5.45%)</title><rect x="1.8182%" y="389" width="5.4545%" height="15" fill="rgb(241,83,37)" fg:x="1" fg:w="3"/><text x="2.0682%" y="399.50">&lt;std::s..</text></g><g><title>core::ffi::c_str::CStr::from_ptr (1 samples, 1.82%)</title><rect x="5.4545%" y="373" width="1.8182%" height="15" fill="rgb(233,36,39)" fg:x="3" fg:w="1"/><text x="5.7045%" y="383.50">c..</text></g><g><title>std::ffi::os_str::OsStr::to_string_lossy (1 samples, 1.82%)</title><rect x="7.2727%" y="405" width="1.8182%" height="15" fill="rgb(226,3,54)" fg:x="4" fg:w="1"/><text x="7.5227%" y="415.50">s..</text></g><g><title>std::sys::unix::os_str::Slice::to_string_lossy (1 samples, 1.82%)</title><rect x="7.2727%" y="389" width="1.8182%" height="15" fill="rgb(245,192,40)" fg:x="4" fg:w="1"/><text x="7.5227%" y="399.50">s..</text></g><g><title>alloc::string::String::from_utf8_lossy (1 samples, 1.82%)</title><rect x="7.2727%" y="373" width="1.8182%" height="15" fill="rgb(238,167,29)" fg:x="4" fg:w="1"/><text x="7.5227%" y="383.50">a..</text></g><g><title>std::path::Path::exists (1 samples, 1.82%)</title><rect x="9.0909%" y="405" width="1.8182%" height="15" fill="rgb(232,182,51)" fg:x="5" fg:w="1"/><text x="9.3409%" y="415.50">s..</text></g><g><title>std::fs::metadata (1 samples, 1.82%)</title><rect x="9.0909%" y="389" width="1.8182%" height="15" fill="rgb(231,60,39)" fg:x="5" fg:w="1"/><text x="9.3409%" y="399.50">s..</text></g><g><title>std::sys::unix::fs::stat (1 samples, 1.82%)</title><rect x="9.0909%" y="373" width="1.8182%" height="15" fill="rgb(208,69,12)" fg:x="5" fg:w="1"/><text x="9.3409%" y="383.50">s..</text></g><g><title>std::sys::unix::fs::try_statx (1 samples, 1.82%)</title><rect x="9.0909%" y="357" width="1.8182%" height="15" fill="rgb(235,93,37)" fg:x="5" fg:w="1"/><text x="9.3409%" y="367.50">s..</text></g><g><title>std::sys::unix::fs::try_statx::statx (1 samples, 1.82%)</title><rect x="9.0909%" y="341" width="1.8182%" height="15" fill="rgb(213,116,39)" fg:x="5" fg:w="1"/><text x="9.3409%" y="351.50">s..</text></g><g><title>statx (1 samples, 1.82%)</title><rect x="9.0909%" y="325" width="1.8182%" height="15" fill="rgb(222,207,29)" fg:x="5" fg:w="1"/><text x="9.3409%" y="335.50">s..</text></g><g><title>std::path::Path::extension (1 samples, 1.82%)</title><rect x="10.9091%" y="405" width="1.8182%" height="15" fill="rgb(206,96,30)" fg:x="6" fg:w="1"/><text x="11.1591%" y="415.50">s..</text></g><g><title>std::path::Path::file_name (1 samples, 1.82%)</title><rect x="10.9091%" y="389" width="1.8182%" height="15" fill="rgb(218,138,4)" fg:x="6" fg:w="1"/><text x="11.1591%" y="399.50">s..</text></g><g><title>&lt;std::path::Components as core::iter::traits::double_ended::DoubleEndedIterator&gt;::next_back (1 samples, 1.82%)</title><rect x="10.9091%" y="373" width="1.8182%" height="15" fill="rgb(250,191,14)" fg:x="6" fg:w="1"/><text x="11.1591%" y="383.50">&lt;..</text></g><g><title>std::path::Components::parse_next_component_back (1 samples, 1.82%)</title><rect x="10.9091%" y="357" width="1.8182%" height="15" fill="rgb(239,60,40)" fg:x="6" fg:w="1"/><text x="11.1591%" y="367.50">s..</text></g><g><title>std::path::Path::exists (1 samples, 1.82%)</title><rect x="12.7273%" y="389" width="1.8182%" height="15" fill="rgb(206,27,48)" fg:x="7" fg:w="1"/><text x="12.9773%" y="399.50">s..</text></g><g><title>std::fs::metadata (1 samples, 1.82%)</title><rect x="12.7273%" y="373" width="1.8182%" height="15" fill="rgb(225,35,8)" fg:x="7" fg:w="1"/><text x="12.9773%" y="383.50">s..</text></g><g><title>std::sys::unix::fs::stat (1 samples, 1.82%)</title><rect x="12.7273%" y="357" width="1.8182%" height="15" fill="rgb(250,213,24)" fg:x="7" fg:w="1"/><text x="12.9773%" y="367.50">s..</text></g><g><title>cfree (1 samples, 1.82%)</title><rect x="12.7273%" y="341" width="1.8182%" height="15" fill="rgb(247,123,22)" fg:x="7" fg:w="1"/><text x="12.9773%" y="351.50">c..</text></g><g><title>subtitler::subtitler_lib::check_and_create_subtitle_for_file (2 samples, 3.64%)</title><rect x="12.7273%" y="405" width="3.6364%" height="15" fill="rgb(231,138,38)" fg:x="7" fg:w="2"/><text x="12.9773%" y="415.50">subt..</text></g><g><title>std::path::Path::join (1 samples, 1.82%)</title><rect x="14.5455%" y="389" width="1.8182%" height="15" fill="rgb(231,145,46)" fg:x="8" fg:w="1"/><text x="14.7955%" y="399.50">s..</text></g><g><title>std::path::Path::_join (1 samples, 1.82%)</title><rect x="14.5455%" y="373" width="1.8182%" height="15" fill="rgb(251,118,11)" fg:x="8" fg:w="1"/><text x="14.7955%" y="383.50">s..</text></g><g><title>&lt;alloc::string::String as core::cmp::PartialEq&lt;&amp;str&gt;&gt;::eq (1 samples, 1.82%)</title><rect x="18.1818%" y="389" width="1.8182%" height="15" fill="rgb(217,147,25)" fg:x="10" fg:w="1"/><text x="18.4318%" y="399.50">&lt;..</text></g><g><title>core::str::traits::&lt;impl core::cmp::PartialEq for str&gt;::eq (1 samples, 1.82%)</title><rect x="18.1818%" y="373" width="1.8182%" height="15" fill="rgb(247,81,37)" fg:x="10" fg:w="1"/><text x="18.4318%" y="383.50">c..</text></g><g><title>core::cmp::impls::&lt;impl core::cmp::PartialEq&lt;&amp;B&gt; for &amp;A&gt;::eq (1 samples, 1.82%)</title><rect x="18.1818%" y="357" width="1.8182%" height="15" fill="rgb(209,12,38)" fg:x="10" fg:w="1"/><text x="18.4318%" y="367.50">c..</text></g><g><title>core::slice::cmp::&lt;impl core::cmp::PartialEq&lt;[B]&gt; for [A]&gt;::eq (1 samples, 1.82%)</title><rect x="18.1818%" y="341" width="1.8182%" height="15" fill="rgb(227,1,9)" fg:x="10" fg:w="1"/><text x="18.4318%" y="351.50">c..</text></g><g><title>&lt;[A] as core::slice::cmp::SlicePartialEq&lt;B&gt;&gt;::equal (1 samples, 1.82%)</title><rect x="18.1818%" y="325" width="1.8182%" height="15" fill="rgb(248,47,43)" fg:x="10" fg:w="1"/><text x="18.4318%" y="335.50">&lt;..</text></g><g><title>&lt;alloc::ffi::c_str::CString as core::convert::From&lt;&amp;core::ffi::c_str::CStr&gt;&gt;::from (4 samples, 7.27%)</title><rect x="23.6364%" y="357" width="7.2727%" height="15" fill="rgb(221,10,30)" fg:x="13" fg:w="4"/><text x="23.8864%" y="367.50">&lt;alloc::ff..</text></g><g><title>alloc::ffi::c_str::&lt;impl alloc::borrow::ToOwned for core::ffi::c_str::CStr&gt;::to_owned (2 samples, 3.64%)</title><rect x="27.2727%" y="341" width="3.6364%" height="15" fill="rgb(210,229,1)" fg:x="15" fg:w="2"/><text x="27.5227%" y="351.50">allo..</text></g><g><title>&lt;T as core::convert::Into&lt;U&gt;&gt;::into (2 samples, 3.64%)</title><rect x="27.2727%" y="325" width="3.6364%" height="15" fill="rgb(222,148,37)" fg:x="15" fg:w="2"/><text x="27.5227%" y="335.50">&lt;T a..</text></g><g><title>&lt;alloc::boxed::Box&lt;[T]&gt; as core::convert::From&lt;&amp;[T]&gt;&gt;::from (2 samples, 3.64%)</title><rect x="27.2727%" y="309" width="3.6364%" height="15" fill="rgb(234,67,33)" fg:x="15" fg:w="2"/><text x="27.5227%" y="319.50">&lt;all..</text></g><g><title>alloc::raw_vec::RawVec&lt;T&gt;::with_capacity (2 samples, 3.64%)</title><rect x="27.2727%" y="293" width="3.6364%" height="15" fill="rgb(247,98,35)" fg:x="15" fg:w="2"/><text x="27.5227%" y="303.50">allo..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::with_capacity_in (2 samples, 3.64%)</title><rect x="27.2727%" y="277" width="3.6364%" height="15" fill="rgb(247,138,52)" fg:x="15" fg:w="2"/><text x="27.5227%" y="287.50">allo..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::allocate_in (2 samples, 3.64%)</title><rect x="27.2727%" y="261" width="3.6364%" height="15" fill="rgb(213,79,30)" fg:x="15" fg:w="2"/><text x="27.5227%" y="271.50">allo..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::allocate (2 samples, 3.64%)</title><rect x="27.2727%" y="245" width="3.6364%" height="15" fill="rgb(246,177,23)" fg:x="15" fg:w="2"/><text x="27.5227%" y="255.50">&lt;all..</text></g><g><title>alloc::alloc::Global::alloc_impl (2 samples, 3.64%)</title><rect x="27.2727%" y="229" width="3.6364%" height="15" fill="rgb(230,62,27)" fg:x="15" fg:w="2"/><text x="27.5227%" y="239.50">allo..</text></g><g><title>alloc::alloc::alloc (2 samples, 3.64%)</title><rect x="27.2727%" y="213" width="3.6364%" height="15" fill="rgb(216,154,8)" fg:x="15" fg:w="2"/><text x="27.5227%" y="223.50">allo..</text></g><g><title>__libc_malloc (2 samples, 3.64%)</title><rect x="27.2727%" y="197" width="3.6364%" height="15" fill="rgb(244,35,45)" fg:x="15" fg:w="2"/><text x="27.5227%" y="207.50">__li..</text></g><g><title>&lt;alloc::sync::Arc&lt;T&gt; as core::clone::Clone&gt;::clone (1 samples, 1.82%)</title><rect x="30.9091%" y="357" width="1.8182%" height="15" fill="rgb(251,115,12)" fg:x="17" fg:w="1"/><text x="31.1591%" y="367.50">&lt;..</text></g><g><title>core::ffi::c_str::CStr::from_ptr (4 samples, 7.27%)</title><rect x="32.7273%" y="357" width="7.2727%" height="15" fill="rgb(240,54,50)" fg:x="18" fg:w="4"/><text x="32.9773%" y="367.50">core::ffi:..</text></g><g><title>__nss_database_lookup (4 samples, 7.27%)</title><rect x="32.7273%" y="341" width="7.2727%" height="15" fill="rgb(233,84,52)" fg:x="18" fg:w="4"/><text x="32.9773%" y="351.50">__nss_data..</text></g><g><title>&lt;std::fs::ReadDir as core::iter::traits::iterator::Iterator&gt;::next (12 samples, 21.82%)</title><rect x="20.0000%" y="389" width="21.8182%" height="15" fill="rgb(207,117,47)" fg:x="11" fg:w="12"/><text x="20.2500%" y="399.50">&lt;std::fs::ReadDir as core::iter::t..</text></g><g><title>&lt;std::sys::unix::fs::ReadDir as core::iter::traits::iterator::Iterator&gt;::next (12 samples, 21.82%)</title><rect x="20.0000%" y="373" width="21.8182%" height="15" fill="rgb(249,43,39)" fg:x="11" fg:w="12"/><text x="20.2500%" y="383.50">&lt;std::sys::unix::fs::ReadDir as co..</text></g><g><title>std::sys::unix::os::set_errno (1 samples, 1.82%)</title><rect x="40.0000%" y="357" width="1.8182%" height="15" fill="rgb(209,38,44)" fg:x="22" fg:w="1"/><text x="40.2500%" y="367.50">s..</text></g><g><title>__errno_location (1 samples, 1.82%)</title><rect x="40.0000%" y="341" width="1.8182%" height="15" fill="rgb(236,212,23)" fg:x="22" fg:w="1"/><text x="40.2500%" y="351.50">_..</text></g><g><title>alloc::str::convert_while_ascii (1 samples, 1.82%)</title><rect x="43.6364%" y="373" width="1.8182%" height="15" fill="rgb(242,79,21)" fg:x="24" fg:w="1"/><text x="43.8864%" y="383.50">a..</text></g><g><title>alloc::vec::Vec&lt;T&gt;::with_capacity (1 samples, 1.82%)</title><rect x="43.6364%" y="357" width="1.8182%" height="15" fill="rgb(211,96,35)" fg:x="24" fg:w="1"/><text x="43.8864%" y="367.50">a..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::with_capacity_in (1 samples, 1.82%)</title><rect x="43.6364%" y="341" width="1.8182%" height="15" fill="rgb(253,215,40)" fg:x="24" fg:w="1"/><text x="43.8864%" y="351.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::with_capacity_in (1 samples, 1.82%)</title><rect x="43.6364%" y="325" width="1.8182%" height="15" fill="rgb(211,81,21)" fg:x="24" fg:w="1"/><text x="43.8864%" y="335.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::allocate_in (1 samples, 1.82%)</title><rect x="43.6364%" y="309" width="1.8182%" height="15" fill="rgb(208,190,38)" fg:x="24" fg:w="1"/><text x="43.8864%" y="319.50">a..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::allocate (1 samples, 1.82%)</title><rect x="43.6364%" y="293" width="1.8182%" height="15" fill="rgb(235,213,38)" fg:x="24" fg:w="1"/><text x="43.8864%" y="303.50">&lt;..</text></g><g><title>alloc::alloc::Global::alloc_impl (1 samples, 1.82%)</title><rect x="43.6364%" y="277" width="1.8182%" height="15" fill="rgb(237,122,38)" fg:x="24" fg:w="1"/><text x="43.8864%" y="287.50">a..</text></g><g><title>alloc::alloc::alloc (1 samples, 1.82%)</title><rect x="43.6364%" y="261" width="1.8182%" height="15" fill="rgb(244,218,35)" fg:x="24" fg:w="1"/><text x="43.8864%" y="271.50">a..</text></g><g><title>__rdl_alloc (1 samples, 1.82%)</title><rect x="43.6364%" y="245" width="1.8182%" height="15" fill="rgb(240,68,47)" fg:x="24" fg:w="1"/><text x="43.8864%" y="255.50">_..</text></g><g><title>alloc::str::&lt;impl str&gt;::to_lowercase (3 samples, 5.45%)</title><rect x="41.8182%" y="389" width="5.4545%" height="15" fill="rgb(210,16,53)" fg:x="23" fg:w="3"/><text x="42.0682%" y="399.50">alloc::..</text></g><g><title>core::unicode::unicode_data::conversions::to_lower (1 samples, 1.82%)</title><rect x="45.4545%" y="373" width="1.8182%" height="15" fill="rgb(235,124,12)" fg:x="25" fg:w="1"/><text x="45.7045%" y="383.50">c..</text></g><g><title>core::option::Option&lt;T&gt;::unwrap_or_default (1 samples, 1.82%)</title><rect x="47.2727%" y="389" width="1.8182%" height="15" fill="rgb(224,169,11)" fg:x="26" fg:w="1"/><text x="47.5227%" y="399.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::string::String&gt; (2 samples, 3.64%)</title><rect x="49.0909%" y="389" width="3.6364%" height="15" fill="rgb(250,166,2)" fg:x="27" fg:w="2"/><text x="49.3409%" y="399.50">core..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::vec::Vec&lt;u8&gt;&gt; (2 samples, 3.64%)</title><rect x="49.0909%" y="373" width="3.6364%" height="15" fill="rgb(242,216,29)" fg:x="27" fg:w="2"/><text x="49.3409%" y="383.50">core..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::raw_vec::RawVec&lt;u8&gt;&gt; (2 samples, 3.64%)</title><rect x="49.0909%" y="357" width="3.6364%" height="15" fill="rgb(230,116,27)" fg:x="27" fg:w="2"/><text x="49.3409%" y="367.50">core..</text></g><g><title>&lt;alloc::raw_vec::RawVec&lt;T,A&gt; as core::ops::drop::Drop&gt;::drop (2 samples, 3.64%)</title><rect x="49.0909%" y="341" width="3.6364%" height="15" fill="rgb(228,99,48)" fg:x="27" fg:w="2"/><text x="49.3409%" y="351.50">&lt;all..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::deallocate (2 samples, 3.64%)</title><rect x="49.0909%" y="325" width="3.6364%" height="15" fill="rgb(253,11,6)" fg:x="27" fg:w="2"/><text x="49.3409%" y="335.50">&lt;all..</text></g><g><title>alloc::alloc::dealloc (2 samples, 3.64%)</title><rect x="49.0909%" y="309" width="3.6364%" height="15" fill="rgb(247,143,39)" fg:x="27" fg:w="2"/><text x="49.3409%" y="319.50">allo..</text></g><g><title>cfree (2 samples, 3.64%)</title><rect x="49.0909%" y="293" width="3.6364%" height="15" fill="rgb(236,97,10)" fg:x="27" fg:w="2"/><text x="49.3409%" y="303.50">cfree</text></g><g><title>core::ptr::drop_in_place&lt;std::fs::DirEntry&gt; (1 samples, 1.82%)</title><rect x="52.7273%" y="389" width="1.8182%" height="15" fill="rgb(233,208,19)" fg:x="29" fg:w="1"/><text x="52.9773%" y="399.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;std::sys::unix::fs::DirEntry&gt; (1 samples, 1.82%)</title><rect x="52.7273%" y="373" width="1.8182%" height="15" fill="rgb(216,164,2)" fg:x="29" fg:w="1"/><text x="52.9773%" y="383.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::sync::Arc&lt;std::sys::unix::fs::InnerReadDir&gt;&gt; (1 samples, 1.82%)</title><rect x="52.7273%" y="357" width="1.8182%" height="15" fill="rgb(220,129,5)" fg:x="29" fg:w="1"/><text x="52.9773%" y="367.50">c..</text></g><g><title>&lt;alloc::sync::Arc&lt;T&gt; as core::ops::drop::Drop&gt;::drop (1 samples, 1.82%)</title><rect x="52.7273%" y="341" width="1.8182%" height="15" fill="rgb(242,17,10)" fg:x="29" fg:w="1"/><text x="52.9773%" y="351.50">&lt;..</text></g><g><title>core::ptr::drop_in_place&lt;std::path::PathBuf&gt; (1 samples, 1.82%)</title><rect x="54.5455%" y="293" width="1.8182%" height="15" fill="rgb(242,107,0)" fg:x="30" fg:w="1"/><text x="54.7955%" y="303.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;std::ffi::os_str::OsString&gt; (1 samples, 1.82%)</title><rect x="54.5455%" y="277" width="1.8182%" height="15" fill="rgb(251,28,31)" fg:x="30" fg:w="1"/><text x="54.7955%" y="287.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;std::sys::unix::os_str::Buf&gt; (1 samples, 1.82%)</title><rect x="54.5455%" y="261" width="1.8182%" height="15" fill="rgb(233,223,10)" fg:x="30" fg:w="1"/><text x="54.7955%" y="271.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::vec::Vec&lt;u8&gt;&gt; (1 samples, 1.82%)</title><rect x="54.5455%" y="245" width="1.8182%" height="15" fill="rgb(215,21,27)" fg:x="30" fg:w="1"/><text x="54.7955%" y="255.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::raw_vec::RawVec&lt;u8&gt;&gt; (1 samples, 1.82%)</title><rect x="54.5455%" y="229" width="1.8182%" height="15" fill="rgb(232,23,21)" fg:x="30" fg:w="1"/><text x="54.7955%" y="239.50">c..</text></g><g><title>&lt;alloc::raw_vec::RawVec&lt;T,A&gt; as core::ops::drop::Drop&gt;::drop (1 samples, 1.82%)</title><rect x="54.5455%" y="213" width="1.8182%" height="15" fill="rgb(244,5,23)" fg:x="30" fg:w="1"/><text x="54.7955%" y="223.50">&lt;..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::deallocate (1 samples, 1.82%)</title><rect x="54.5455%" y="197" width="1.8182%" height="15" fill="rgb(226,81,46)" fg:x="30" fg:w="1"/><text x="54.7955%" y="207.50">&lt;..</text></g><g><title>alloc::alloc::dealloc (1 samples, 1.82%)</title><rect x="54.5455%" y="181" width="1.8182%" height="15" fill="rgb(247,70,30)" fg:x="30" fg:w="1"/><text x="54.7955%" y="191.50">a..</text></g><g><title>cfree (1 samples, 1.82%)</title><rect x="54.5455%" y="165" width="1.8182%" height="15" fill="rgb(212,68,19)" fg:x="30" fg:w="1"/><text x="54.7955%" y="175.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;std::fs::ReadDir&gt; (2 samples, 3.64%)</title><rect x="54.5455%" y="389" width="3.6364%" height="15" fill="rgb(240,187,13)" fg:x="30" fg:w="2"/><text x="54.7955%" y="399.50">core..</text></g><g><title>core::ptr::drop_in_place&lt;std::sys::unix::fs::ReadDir&gt; (2 samples, 3.64%)</title><rect x="54.5455%" y="373" width="3.6364%" height="15" fill="rgb(223,113,26)" fg:x="30" fg:w="2"/><text x="54.7955%" y="383.50">core..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::sync::Arc&lt;std::sys::unix::fs::InnerReadDir&gt;&gt; (2 samples, 3.64%)</title><rect x="54.5455%" y="357" width="3.6364%" height="15" fill="rgb(206,192,2)" fg:x="30" fg:w="2"/><text x="54.7955%" y="367.50">core..</text></g><g><title>&lt;alloc::sync::Arc&lt;T&gt; as core::ops::drop::Drop&gt;::drop (2 samples, 3.64%)</title><rect x="54.5455%" y="341" width="3.6364%" height="15" fill="rgb(241,108,4)" fg:x="30" fg:w="2"/><text x="54.7955%" y="351.50">&lt;all..</text></g><g><title>alloc::sync::Arc&lt;T&gt;::drop_slow (2 samples, 3.64%)</title><rect x="54.5455%" y="325" width="3.6364%" height="15" fill="rgb(247,173,49)" fg:x="30" fg:w="2"/><text x="54.7955%" y="335.50">allo..</text></g><g><title>core::ptr::drop_in_place&lt;std::sys::unix::fs::InnerReadDir&gt; (2 samples, 3.64%)</title><rect x="54.5455%" y="309" width="3.6364%" height="15" fill="rgb(224,114,35)" fg:x="30" fg:w="2"/><text x="54.7955%" y="319.50">core..</text></g><g><title>core::ptr::drop_in_place&lt;std::sys::unix::fs::Dir&gt; (1 samples, 1.82%)</title><rect x="56.3636%" y="293" width="1.8182%" height="15" fill="rgb(245,159,27)" fg:x="31" fg:w="1"/><text x="56.6136%" y="303.50">c..</text></g><g><title>&lt;std::sys::unix::fs::Dir as core::ops::drop::Drop&gt;::drop (1 samples, 1.82%)</title><rect x="56.3636%" y="277" width="1.8182%" height="15" fill="rgb(245,172,44)" fg:x="31" fg:w="1"/><text x="56.6136%" y="287.50">&lt;..</text></g><g><title>closedir (1 samples, 1.82%)</title><rect x="56.3636%" y="261" width="1.8182%" height="15" fill="rgb(236,23,11)" fg:x="31" fg:w="1"/><text x="56.6136%" y="271.50">c..</text></g><g><title>pthread_attr_setschedparam (1 samples, 1.82%)</title><rect x="56.3636%" y="245" width="1.8182%" height="15" fill="rgb(205,117,38)" fg:x="31" fg:w="1"/><text x="56.6136%" y="255.50">p..</text></g><g><title>std::ffi::os_str::OsStr::to_string_lossy (4 samples, 7.27%)</title><rect x="58.1818%" y="389" width="7.2727%" height="15" fill="rgb(237,72,25)" fg:x="32" fg:w="4"/><text x="58.4318%" y="399.50">std::ffi::..</text></g><g><title>std::sys::unix::os_str::Slice::to_string_lossy (4 samples, 7.27%)</title><rect x="58.1818%" y="373" width="7.2727%" height="15" fill="rgb(244,70,9)" fg:x="32" fg:w="4"/><text x="58.4318%" y="383.50">std::sys::..</text></g><g><title>alloc::string::String::from_utf8_lossy (3 samples, 5.45%)</title><rect x="60.0000%" y="357" width="5.4545%" height="15" fill="rgb(217,125,39)" fg:x="33" fg:w="3"/><text x="60.2500%" y="367.50">alloc::..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::as_mut_ptr (1 samples, 1.82%)</title><rect x="65.4545%" y="213" width="1.8182%" height="15" fill="rgb(235,36,10)" fg:x="36" fg:w="1"/><text x="65.7045%" y="223.50">a..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::ptr (1 samples, 1.82%)</title><rect x="65.4545%" y="197" width="1.8182%" height="15" fill="rgb(251,123,47)" fg:x="36" fg:w="1"/><text x="65.7045%" y="207.50">a..</text></g><g><title>&lt;core::result::Result&lt;T,E&gt; as core::ops::try_trait::Try&gt;::branch (1 samples, 1.82%)</title><rect x="67.2727%" y="149" width="1.8182%" height="15" fill="rgb(221,13,13)" fg:x="37" fg:w="1"/><text x="67.5227%" y="159.50">&lt;..</text></g><g><title>__nss_database_lookup (1 samples, 1.82%)</title><rect x="70.9091%" y="53" width="1.8182%" height="15" fill="rgb(238,131,9)" fg:x="39" fg:w="1"/><text x="71.1591%" y="63.50">_..</text></g><g><title>std::fs::DirEntry::path (7 samples, 12.73%)</title><rect x="65.4545%" y="389" width="12.7273%" height="15" fill="rgb(211,50,8)" fg:x="36" fg:w="7"/><text x="65.7045%" y="399.50">std::fs::DirEntry::..</text></g><g><title>std::sys::unix::fs::DirEntry::path (7 samples, 12.73%)</title><rect x="65.4545%" y="373" width="12.7273%" height="15" fill="rgb(245,182,24)" fg:x="36" fg:w="7"/><text x="65.7045%" y="383.50">std::sys::unix::fs:..</text></g><g><title>std::path::Path::join (7 samples, 12.73%)</title><rect x="65.4545%" y="357" width="12.7273%" height="15" fill="rgb(242,14,37)" fg:x="36" fg:w="7"/><text x="65.7045%" y="367.50">std::path::Path::jo..</text></g><g><title>std::path::Path::_join (7 samples, 12.73%)</title><rect x="65.4545%" y="341" width="12.7273%" height="15" fill="rgb(246,228,12)" fg:x="36" fg:w="7"/><text x="65.7045%" y="351.50">std::path::Path::_j..</text></g><g><title>std::path::PathBuf::push (7 samples, 12.73%)</title><rect x="65.4545%" y="325" width="12.7273%" height="15" fill="rgb(213,55,15)" fg:x="36" fg:w="7"/><text x="65.7045%" y="335.50">std::path::PathBuf:..</text></g><g><title>std::path::PathBuf::_push (7 samples, 12.73%)</title><rect x="65.4545%" y="309" width="12.7273%" height="15" fill="rgb(209,9,3)" fg:x="36" fg:w="7"/><text x="65.7045%" y="319.50">std::path::PathBuf:..</text></g><g><title>std::ffi::os_str::OsString::push (7 samples, 12.73%)</title><rect x="65.4545%" y="293" width="12.7273%" height="15" fill="rgb(230,59,30)" fg:x="36" fg:w="7"/><text x="65.7045%" y="303.50">std::ffi::os_str::O..</text></g><g><title>std::sys::unix::os_str::Buf::push_slice (7 samples, 12.73%)</title><rect x="65.4545%" y="277" width="12.7273%" height="15" fill="rgb(209,121,21)" fg:x="36" fg:w="7"/><text x="65.7045%" y="287.50">std::sys::unix::os_..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::extend_from_slice (7 samples, 12.73%)</title><rect x="65.4545%" y="261" width="12.7273%" height="15" fill="rgb(220,109,13)" fg:x="36" fg:w="7"/><text x="65.7045%" y="271.50">alloc::vec::Vec&lt;T,A..</text></g><g><title>&lt;alloc::vec::Vec&lt;T,A&gt; as alloc::vec::spec_extend::SpecExtend&lt;&amp;T,core::slice::iter::Iter&lt;T&gt;&gt;&gt;::spec_extend (7 samples, 12.73%)</title><rect x="65.4545%" y="245" width="12.7273%" height="15" fill="rgb(232,18,1)" fg:x="36" fg:w="7"/><text x="65.7045%" y="255.50">&lt;alloc::vec::Vec&lt;T,..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::append_elements (7 samples, 12.73%)</title><rect x="65.4545%" y="229" width="12.7273%" height="15" fill="rgb(215,41,42)" fg:x="36" fg:w="7"/><text x="65.7045%" y="239.50">alloc::vec::Vec&lt;T,A..</text></g><g><title>alloc::vec::Vec&lt;T,A&gt;::reserve (6 samples, 10.91%)</title><rect x="67.2727%" y="213" width="10.9091%" height="15" fill="rgb(224,123,36)" fg:x="37" fg:w="6"/><text x="67.5227%" y="223.50">alloc::vec::Vec&lt;..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::reserve (6 samples, 10.91%)</title><rect x="67.2727%" y="197" width="10.9091%" height="15" fill="rgb(240,125,3)" fg:x="37" fg:w="6"/><text x="67.5227%" y="207.50">alloc::raw_vec::..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::reserve::do_reserve_and_handle (6 samples, 10.91%)</title><rect x="67.2727%" y="181" width="10.9091%" height="15" fill="rgb(205,98,50)" fg:x="37" fg:w="6"/><text x="67.5227%" y="191.50">alloc::raw_vec::..</text></g><g><title>alloc::raw_vec::RawVec&lt;T,A&gt;::grow_amortized (6 samples, 10.91%)</title><rect x="67.2727%" y="165" width="10.9091%" height="15" fill="rgb(205,185,37)" fg:x="37" fg:w="6"/><text x="67.5227%" y="175.50">alloc::raw_vec::..</text></g><g><title>alloc::raw_vec::finish_grow (5 samples, 9.09%)</title><rect x="69.0909%" y="149" width="9.0909%" height="15" fill="rgb(238,207,15)" fg:x="38" fg:w="5"/><text x="69.3409%" y="159.50">alloc::raw_ve..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::grow (5 samples, 9.09%)</title><rect x="69.0909%" y="133" width="9.0909%" height="15" fill="rgb(213,199,42)" fg:x="38" fg:w="5"/><text x="69.3409%" y="143.50">&lt;alloc::alloc..</text></g><g><title>alloc::alloc::Global::grow_impl (5 samples, 9.09%)</title><rect x="69.0909%" y="117" width="9.0909%" height="15" fill="rgb(235,201,11)" fg:x="38" fg:w="5"/><text x="69.3409%" y="127.50">alloc::alloc:..</text></g><g><title>alloc::alloc::realloc (5 samples, 9.09%)</title><rect x="69.0909%" y="101" width="9.0909%" height="15" fill="rgb(207,46,11)" fg:x="38" fg:w="5"/><text x="69.3409%" y="111.50">alloc::alloc:..</text></g><g><title>realloc (5 samples, 9.09%)</title><rect x="69.0909%" y="85" width="9.0909%" height="15" fill="rgb(241,35,35)" fg:x="38" fg:w="5"/><text x="69.3409%" y="95.50">realloc</text></g><g><title>pthread_attr_setschedparam (5 samples, 9.09%)</title><rect x="69.0909%" y="69" width="9.0909%" height="15" fill="rgb(243,32,47)" fg:x="38" fg:w="5"/><text x="69.3409%" y="79.50">pthread_attr_..</text></g><g><title>pthread_attr_setschedparam (3 samples, 5.45%)</title><rect x="72.7273%" y="53" width="5.4545%" height="15" fill="rgb(247,202,23)" fg:x="40" fg:w="3"/><text x="72.9773%" y="63.50">pthread..</text></g><g><title>pthread_attr_setschedparam (1 samples, 1.82%)</title><rect x="76.3636%" y="37" width="1.8182%" height="15" fill="rgb(219,102,11)" fg:x="42" fg:w="1"/><text x="76.6136%" y="47.50">p..</text></g><g><title>std::fs::read_dir (2 samples, 3.64%)</title><rect x="78.1818%" y="389" width="3.6364%" height="15" fill="rgb(243,110,44)" fg:x="43" fg:w="2"/><text x="78.4318%" y="399.50">std:..</text></g><g><title>std::sys::unix::fs::readdir (2 samples, 3.64%)</title><rect x="78.1818%" y="373" width="3.6364%" height="15" fill="rgb(222,74,54)" fg:x="43" fg:w="2"/><text x="78.4318%" y="383.50">std:..</text></g><g><title>opendir (2 samples, 3.64%)</title><rect x="78.1818%" y="357" width="3.6364%" height="15" fill="rgb(216,99,12)" fg:x="43" fg:w="2"/><text x="78.4318%" y="367.50">open..</text></g><g><title>__fxstat64 (2 samples, 3.64%)</title><rect x="78.1818%" y="341" width="3.6364%" height="15" fill="rgb(226,22,26)" fg:x="43" fg:w="2"/><text x="78.4318%" y="351.50">__fx..</text></g><g><title>std::path::Path::exists (1 samples, 1.82%)</title><rect x="81.8182%" y="389" width="1.8182%" height="15" fill="rgb(217,163,10)" fg:x="45" fg:w="1"/><text x="82.0682%" y="399.50">s..</text></g><g><title>std::fs::metadata (1 samples, 1.82%)</title><rect x="81.8182%" y="373" width="1.8182%" height="15" fill="rgb(213,25,53)" fg:x="45" fg:w="1"/><text x="82.0682%" y="383.50">s..</text></g><g><title>std::sys::unix::fs::stat (1 samples, 1.82%)</title><rect x="81.8182%" y="357" width="1.8182%" height="15" fill="rgb(252,105,26)" fg:x="45" fg:w="1"/><text x="82.0682%" y="367.50">s..</text></g><g><title>std::sys::unix::fs::try_statx (1 samples, 1.82%)</title><rect x="81.8182%" y="341" width="1.8182%" height="15" fill="rgb(220,39,43)" fg:x="45" fg:w="1"/><text x="82.0682%" y="351.50">s..</text></g><g><title>std::sys::unix::fs::try_statx::statx (1 samples, 1.82%)</title><rect x="81.8182%" y="325" width="1.8182%" height="15" fill="rgb(229,68,48)" fg:x="45" fg:w="1"/><text x="82.0682%" y="335.50">s..</text></g><g><title>statx (1 samples, 1.82%)</title><rect x="81.8182%" y="309" width="1.8182%" height="15" fill="rgb(252,8,32)" fg:x="45" fg:w="1"/><text x="82.0682%" y="319.50">s..</text></g><g><title>core::option::Option&lt;T&gt;::map (1 samples, 1.82%)</title><rect x="83.6364%" y="373" width="1.8182%" height="15" fill="rgb(223,20,43)" fg:x="46" fg:w="1"/><text x="83.8864%" y="383.50">c..</text></g><g><title>core::ops::function::FnOnce::call_once (1 samples, 1.82%)</title><rect x="83.6364%" y="357" width="1.8182%" height="15" fill="rgb(229,81,49)" fg:x="46" fg:w="1"/><text x="83.8864%" y="367.50">c..</text></g><g><title>std::path::rsplit_file_at_dot (1 samples, 1.82%)</title><rect x="83.6364%" y="341" width="1.8182%" height="15" fill="rgb(236,28,36)" fg:x="46" fg:w="1"/><text x="83.8864%" y="351.50">s..</text></g><g><title>&lt;core::slice::iter::Iter&lt;T&gt; as core::iter::traits::double_ended::DoubleEndedIterator&gt;::next_back (1 samples, 1.82%)</title><rect x="90.9091%" y="309" width="1.8182%" height="15" fill="rgb(249,185,26)" fg:x="50" fg:w="1"/><text x="91.1591%" y="319.50">&lt;..</text></g><g><title>std::path::Path::extension (6 samples, 10.91%)</title><rect x="83.6364%" y="389" width="10.9091%" height="15" fill="rgb(249,174,33)" fg:x="46" fg:w="6"/><text x="83.8864%" y="399.50">std::path::Path:..</text></g><g><title>std::path::Path::file_name (5 samples, 9.09%)</title><rect x="85.4545%" y="373" width="9.0909%" height="15" fill="rgb(233,201,37)" fg:x="47" fg:w="5"/><text x="85.7045%" y="383.50">std::path::Pa..</text></g><g><title>&lt;std::path::Components as core::iter::traits::double_ended::DoubleEndedIterator&gt;::next_back (5 samples, 9.09%)</title><rect x="85.4545%" y="357" width="9.0909%" height="15" fill="rgb(221,78,26)" fg:x="47" fg:w="5"/><text x="85.7045%" y="367.50">&lt;std::path::C..</text></g><g><title>std::path::Components::parse_next_component_back (3 samples, 5.45%)</title><rect x="89.0909%" y="341" width="5.4545%" height="15" fill="rgb(250,127,30)" fg:x="49" fg:w="3"/><text x="89.3409%" y="351.50">std::pa..</text></g><g><title>&lt;core::slice::iter::Iter&lt;T&gt; as core::iter::traits::iterator::Iterator&gt;::rposition (2 samples, 3.64%)</title><rect x="90.9091%" y="325" width="3.6364%" height="15" fill="rgb(230,49,44)" fg:x="50" fg:w="2"/><text x="91.1591%" y="335.50">&lt;cor..</text></g><g><title>std::path::Components::parse_next_component_back::{{closure}} (1 samples, 1.82%)</title><rect x="92.7273%" y="309" width="1.8182%" height="15" fill="rgb(229,67,23)" fg:x="51" fg:w="1"/><text x="92.9773%" y="319.50">s..</text></g><g><title>std::path::Components::is_sep_byte (1 samples, 1.82%)</title><rect x="92.7273%" y="293" width="1.8182%" height="15" fill="rgb(249,83,47)" fg:x="51" fg:w="1"/><text x="92.9773%" y="303.50">s..</text></g><g><title>core::ptr::drop_in_place&lt;std::path::PathBuf&gt; (1 samples, 1.82%)</title><rect x="94.5455%" y="373" width="1.8182%" height="15" fill="rgb(215,43,3)" fg:x="52" fg:w="1"/><text x="94.7955%" y="383.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;std::ffi::os_str::OsString&gt; (1 samples, 1.82%)</title><rect x="94.5455%" y="357" width="1.8182%" height="15" fill="rgb(238,154,13)" fg:x="52" fg:w="1"/><text x="94.7955%" y="367.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;std::sys::unix::os_str::Buf&gt; (1 samples, 1.82%)</title><rect x="94.5455%" y="341" width="1.8182%" height="15" fill="rgb(219,56,2)" fg:x="52" fg:w="1"/><text x="94.7955%" y="351.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::vec::Vec&lt;u8&gt;&gt; (1 samples, 1.82%)</title><rect x="94.5455%" y="325" width="1.8182%" height="15" fill="rgb(233,0,4)" fg:x="52" fg:w="1"/><text x="94.7955%" y="335.50">c..</text></g><g><title>core::ptr::drop_in_place&lt;alloc::raw_vec::RawVec&lt;u8&gt;&gt; (1 samples, 1.82%)</title><rect x="94.5455%" y="309" width="1.8182%" height="15" fill="rgb(235,30,7)" fg:x="52" fg:w="1"/><text x="94.7955%" y="319.50">c..</text></g><g><title>&lt;alloc::raw_vec::RawVec&lt;T,A&gt; as core::ops::drop::Drop&gt;::drop (1 samples, 1.82%)</title><rect x="94.5455%" y="293" width="1.8182%" height="15" fill="rgb(250,79,13)" fg:x="52" fg:w="1"/><text x="94.7955%" y="303.50">&lt;..</text></g><g><title>&lt;alloc::alloc::Global as core::alloc::Allocator&gt;::deallocate (1 samples, 1.82%)</title><rect x="94.5455%" y="277" width="1.8182%" height="15" fill="rgb(211,146,34)" fg:x="52" fg:w="1"/><text x="94.7955%" y="287.50">&lt;..</text></g><g><title>alloc::alloc::dealloc (1 samples, 1.82%)</title><rect x="94.5455%" y="261" width="1.8182%" height="15" fill="rgb(228,22,38)" fg:x="52" fg:w="1"/><text x="94.7955%" y="271.50">a..</text></g><g><title>cfree (1 samples, 1.82%)</title><rect x="94.5455%" y="245" width="1.8182%" height="15" fill="rgb(235,168,5)" fg:x="52" fg:w="1"/><text x="94.7955%" y="255.50">c..</text></g><g><title>subtitler::subtitler_lib::check_and_create_subtitle_for_file (2 samples, 3.64%)</title><rect x="94.5455%" y="389" width="3.6364%" height="15" fill="rgb(221,155,16)" fg:x="52" fg:w="2"/><text x="94.7955%" y="399.50">subt..</text></g><g><title>std::path::Path::join (1 samples, 1.82%)</title><rect x="96.3636%" y="373" width="1.8182%" height="15" fill="rgb(215,215,53)" fg:x="53" fg:w="1"/><text x="96.6136%" y="383.50">s..</text></g><g><title>std::path::Path::_join (1 samples, 1.82%)</title><rect x="96.3636%" y="357" width="1.8182%" height="15" fill="rgb(223,4,10)" fg:x="53" fg:w="1"/><text x="96.6136%" y="367.50">s..</text></g><g><title>std::path::PathBuf::push (1 samples, 1.82%)</title><rect x="96.3636%" y="341" width="1.8182%" height="15" fill="rgb(234,103,6)" fg:x="53" fg:w="1"/><text x="96.6136%" y="351.50">s..</text></g><g><title>std::path::PathBuf::_push (1 samples, 1.82%)</title><rect x="96.3636%" y="325" width="1.8182%" height="15" fill="rgb(227,97,0)" fg:x="53" fg:w="1"/><text x="96.6136%" y="335.50">s..</text></g><g><title>std::path::Path::is_absolute (1 samples, 1.82%)</title><rect x="96.3636%" y="309" width="1.8182%" height="15" fill="rgb(234,150,53)" fg:x="53" fg:w="1"/><text x="96.6136%" y="319.50">s..</text></g><g><title>std::path::Path::has_root (1 samples, 1.82%)</title><rect x="96.3636%" y="293" width="1.8182%" height="15" fill="rgb(228,201,54)" fg:x="53" fg:w="1"/><text x="96.6136%" y="303.50">s..</text></g><g><title>std::path::Path::components (1 samples, 1.82%)</title><rect x="96.3636%" y="277" width="1.8182%" height="15" fill="rgb(222,22,37)" fg:x="53" fg:w="1"/><text x="96.6136%" y="287.50">s..</text></g><g><title>all (55 samples, 100%)</title><rect x="0.0000%" y="741" width="100.0000%" height="15" fill="rgb(237,53,32)" fg:x="0" fg:w="55"/><text x="0.2500%" y="751.50"></text></g><g><title>subtitler (55 samples, 100.00%)</title><rect x="0.0000%" y="725" width="100.0000%" height="15" fill="rgb(233,25,53)" fg:x="0" fg:w="55"/><text x="0.2500%" y="735.50">subtitler</text></g><g><title>_start (55 samples, 100.00%)</title><rect x="0.0000%" y="709" width="100.0000%" height="15" fill="rgb(210,40,34)" fg:x="0" fg:w="55"/><text x="0.2500%" y="719.50">_start</text></g><g><title>__libc_start_main (55 samples, 100.00%)</title><rect x="0.0000%" y="693" width="100.0000%" height="15" fill="rgb(241,220,44)" fg:x="0" fg:w="55"/><text x="0.2500%" y="703.50">__libc_start_main</text></g><g><title>main (55 samples, 100.00%)</title><rect x="0.0000%" y="677" width="100.0000%" height="15" fill="rgb(235,28,35)" fg:x="0" fg:w="55"/><text x="0.2500%" y="687.50">main</text></g><g><title>std::rt::lang_start_internal (55 samples, 100.00%)</title><rect x="0.0000%" y="661" width="100.0000%" height="15" fill="rgb(210,56,17)" fg:x="0" fg:w="55"/><text x="0.2500%" y="671.50">std::rt::lang_start_internal</text></g><g><title>std::panic::catch_unwind (55 samples, 100.00%)</title><rect x="0.0000%" y="645" width="100.0000%" height="15" fill="rgb(224,130,29)" fg:x="0" fg:w="55"/><text x="0.2500%" y="655.50">std::panic::catch_unwind</text></g><g><title>std::panicking::try (55 samples, 100.00%)</title><rect x="0.0000%" y="629" width="100.0000%" height="15" fill="rgb(235,212,8)" fg:x="0" fg:w="55"/><text x="0.2500%" y="639.50">std::panicking::try</text></g><g><title>std::panicking::try::do_call (55 samples, 100.00%)</title><rect x="0.0000%" y="613" width="100.0000%" height="15" fill="rgb(223,33,50)" fg:x="0" fg:w="55"/><text x="0.2500%" y="623.50">std::panicking::try::do_call</text></g><g><title>std::rt::lang_start_internal::{{closure}} (55 samples, 100.00%)</title><rect x="0.0000%" y="597" width="100.0000%" height="15" fill="rgb(219,149,13)" fg:x="0" fg:w="55"/><text x="0.2500%" y="607.50">std::rt::lang_start_internal::{{closure}}</text></g><g><title>std::panic::catch_unwind (55 samples, 100.00%)</title><rect x="0.0000%" y="581" width="100.0000%" height="15" fill="rgb(250,156,29)" fg:x="0" fg:w="55"/><text x="0.2500%" y="591.50">std::panic::catch_unwind</text></g><g><title>std::panicking::try (55 samples, 100.00%)</title><rect x="0.0000%" y="565" width="100.0000%" height="15" fill="rgb(216,193,19)" fg:x="0" fg:w="55"/><text x="0.2500%" y="575.50">std::panicking::try</text></g><g><title>std::panicking::try::do_call (55 samples, 100.00%)</title><rect x="0.0000%" y="549" width="100.0000%" height="15" fill="rgb(216,135,14)" fg:x="0" fg:w="55"/><text x="0.2500%" y="559.50">std::panicking::try::do_call</text></g><g><title>core::ops::function::impls::&lt;impl core::ops::function::FnOnce&lt;A&gt; for &amp;F&gt;::call_once (55 samples, 100.00%)</title><rect x="0.0000%" y="533" width="100.0000%" height="15" fill="rgb(241,47,5)" fg:x="0" fg:w="55"/><text x="0.2500%" y="543.50">core::ops::function::impls::&lt;impl core::ops::function::FnOnce&lt;A&gt; for &amp;F&gt;::call_once</text></g><g><title>std::rt::lang_start::{{closure}} (55 samples, 100.00%)</title><rect x="0.0000%" y="517" width="100.0000%" height="15" fill="rgb(233,42,35)" fg:x="0" fg:w="55"/><text x="0.2500%" y="527.50">std::rt::lang_start::{{closure}}</text></g><g><title>std::sys_common::backtrace::__rust_begin_short_backtrace (55 samples, 100.00%)</title><rect x="0.0000%" y="501" width="100.0000%" height="15" fill="rgb(231,13,6)" fg:x="0" fg:w="55"/><text x="0.2500%" y="511.50">std::sys_common::backtrace::__rust_begin_short_backtrace</text></g><g><title>core::ops::function::FnOnce::call_once (55 samples, 100.00%)</title><rect x="0.0000%" y="485" width="100.0000%" height="15" fill="rgb(207,181,40)" fg:x="0" fg:w="55"/><text x="0.2500%" y="495.50">core::ops::function::FnOnce::call_once</text></g><g><title>subtitler::main (55 samples, 100.00%)</title><rect x="0.0000%" y="469" width="100.0000%" height="15" fill="rgb(254,173,49)" fg:x="0" fg:w="55"/><text x="0.2500%" y="479.50">subtitler::main</text></g><g><title>subtitler::subtitler_lib::find_files (55 samples, 100.00%)</title><rect x="0.0000%" y="453" width="100.0000%" height="15" fill="rgb(221,1,38)" fg:x="0" fg:w="55"/><text x="0.2500%" y="463.50">subtitler::subtitler_lib::find_files</text></g><g><title>subtitler::subtitler_lib::find_files (55 samples, 100.00%)</title><rect x="0.0000%" y="437" width="100.0000%" height="15" fill="rgb(206,124,46)" fg:x="0" fg:w="55"/><text x="0.2500%" y="447.50">subtitler::subtitler_lib::find_files</text></g><g><title>subtitler::subtitler_lib::find_files (54 samples, 98.18%)</title><rect x="1.8182%" y="421" width="98.1818%" height="15" fill="rgb(249,21,11)" fg:x="1" fg:w="54"/><text x="2.0682%" y="431.50">subtitler::subtitler_lib::find_files</text></g><g><title>subtitler::subtitler_lib::find_files (46 samples, 83.64%)</title><rect x="16.3636%" y="405" width="83.6364%" height="15" fill="rgb(222,201,40)" fg:x="9" fg:w="46"/><text x="16.6136%" y="415.50">subtitler::subtitler_lib::find_files</text></g><g><title>subtitler::subtitler_lib::find_files (1 samples, 1.82%)</title><rect x="98.1818%" y="389" width="1.8182%" height="15" fill="rgb(235,61,29)" fg:x="54" fg:w="1"/><text x="98.4318%" y="399.50">s..</text></g><g><title>subtitler::subtitler_lib::find_files (1 samples, 1.82%)</title><rect x="98.1818%" y="373" width="1.8182%" height="15" fill="rgb(219,207,3)" fg:x="54" fg:w="1"/><text x="98.4318%" y="383.50">s..</text></g><g><title>&lt;std::fs::ReadDir as core::iter::traits::iterator::Iterator&gt;::next (1 samples, 1.82%)</title><rect x="98.1818%" y="357" width="1.8182%" height="15" fill="rgb(222,56,46)" fg:x="54" fg:w="1"/><text x="98.4318%" y="367.50">&lt;..</text></g><g><title>core::option::Option&lt;T&gt;::map (1 samples, 1.82%)</title><rect x="98.1818%" y="341" width="1.8182%" height="15" fill="rgb(239,76,54)" fg:x="54" fg:w="1"/><text x="98.4318%" y="351.50">c..</text></g></svg></svg>