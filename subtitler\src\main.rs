use std::{time::{Instant}, env};
use std::path::Path;
use colored::*;

mod subtitler_lib;
fn main() {
    let defpath = if cfg!(windows) {
        r#"E:\Downloads\Completed"#.to_string()
    } else {
        r#"/mnt/e/Downloads/Completed"#.to_string()
    };
    
    //let path = "E:\\".to_owned();
    let path=env::args().nth(1).unwrap_or(defpath);
    let b: bool = Path::new(&path).is_dir();
    if  b ==false
    {
        println!("{} : '{}'\n{}: subtitler dirpath(root directory to search for video files)",
            "Directory Not Found".red().bold(),
            path.yellow(),
            "Usage".cyan().bold());
        println!("{}:\n{}", "example".cyan().bold(), "subtitler.exe X:\\Videos".green());
        return ;
    }
    let now = Instant::now();
    println!("{} {}", "🔍 Searching".blue().bold(), path.yellow());
    subtitler_lib::find_files(&path);
    println!("{} {}", "🔄 Search again in".blue().bold(), path.yellow());
    println!("{} {} in {} {}\n",
        "✅ Done Searching".green().bold(),
        path.yellow(),
        now.elapsed().as_millis().to_string().cyan().bold(),
        "ms".cyan());
}
