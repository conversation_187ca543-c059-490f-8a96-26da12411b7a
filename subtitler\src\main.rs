use std::{time::{Instant}, env};
use std::path::Path;

mod subtitler_lib;
fn main() {
    let defpath = if cfg!(windows) {
        r#"E:\Downloads\Completed"#.to_string()
    } else {
        r#"/mnt/e/Downloads/Completed"#.to_string()
    };
    
    //let path = "E:\\".to_owned();
    let path=env::args().nth(1).unwrap_or(defpath);
    let b: bool = Path::new(&path).is_dir();
    if  b ==false
    {
        println!("Directory Not Found : '{path}'\nUsage: subtitler dirpath(root directory to search for video files)"); 
        println!("example:\nsubtitler.exe X:\\Videos"); 
        return ;
    }
    let now = Instant::now();
    println!("Searching {path}");
    subtitler_lib::find_files(&path);
    println!("search again in {path}");
    println!("Done Searching {path} in {} ms\n", now.elapsed().as_millis());
}
